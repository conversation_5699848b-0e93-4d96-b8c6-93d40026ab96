import { defineMiddleware } from 'astro:middleware'
import { U<PERSON>ars<PERSON> } from 'ua-parser-js'
import { isMobile } from '@/stores/isMobile'

export const onRequest = defineMiddleware(async (context, next) => {
  // //console.log('mdlwr: ', {cookie: context.cookies, url: context.url});

  // //console.log('context.cookies _cart', context.cookies.get('_cart'))

  const userAgent = context.request.headers.get('user-agent') || ''
  const parser = new UAParser(userAgent)

  const initialIsMobile = parser.getDevice().type === 'mobile'
  context.locals.isMobile = initialIsMobile

  context.locals.isIOS = parser.getOS().is('iOS')

  const viewMode = context.cookies.get('view-mode')?.value
  context.locals.viewMode = initialIsMobile ? 'grid' : viewMode ? (viewMode === 'grid' ? 'grid' : 'table') : 'table'
  // Добавляем обработку режима поиска
  const searchMode = context.cookies.get('search-mode')?.value as 'flat' | 'category' | undefined
  context.locals.searchMode = searchMode || 'flat'

  context.locals.isAdmin = import.meta.env.DEV
  const ctoken = context.cookies.get('ctoken')?.value

  // console.log("🚀 ~ onRequest ~ ctoken:", ctoken)

  if (ctoken) {
    try {
      const res = await fetch(`${context.url.origin}/api/cpan/auth/check`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${ctoken}`
        }
      })

      if (res.ok) {
        const data = await res.json()
        // console.log('🚀 ~ isAdmin onRequest ~ data:', data)
        if (data?.user_id) {
          context.locals.isAdmin = true
        }
      }
    } catch (error) {
      // Ошибка проверки администратора, оставляем isAdmin = false
      console.error('Error checking admin status:', error)
    }
  }

  // --- Миграция httpOnly куки через query-параметры (rti-baltika.ru -> mirsalnikov.ru) ---
  const cartCookie = context.cookies.get('_cart')?.value
  const sessionCookie = context.cookies.get('rti-session')?.value
  const migratedCookie = context.cookies.get('2rti-cookies-migrated-success')?.value

  if (context.url.hostname === 'rti-baltika.ru') {
    // Если есть migrated=1, устанавливаем куку миграции на rti-baltika.ru и убираем параметр
    const migratedParam = context.url.searchParams.get('migrated')
    if (migratedParam === '1') {
      // Устанавливаем куку миграции на rti-baltika.ru
      context.cookies.set('2rti-cookies-migrated-success', 'true', {
        path: '/',
        httpOnly: true,
        secure: true,
        sameSite: 'none',
        maxAge: 60 * 60 * 24 * 365
      })

      // Редиректим на чистый URL без параметра migrated
      const cleanUrl = new URL(context.url)
      cleanUrl.searchParams.delete('migrated')

      // Если остались другие параметры, сохраняем их
      const finalUrl = cleanUrl.search ? `${cleanUrl.pathname}${cleanUrl.search}` : cleanUrl.pathname

      return new Response(null, {
        status: 302,
        headers: {
          Location: finalUrl
        }
      })
    }

    // Редиректим на mirsalnikov.ru только если миграция еще не была выполнена И есть куки для миграции
    if (!migratedCookie && (cartCookie || sessionCookie)) {
      const url = new URL(`https://mirsalnikov.ru${context.url.pathname}${context.url.search}`)
      if (cartCookie) url.searchParams.set('cart', cartCookie)
      if (sessionCookie) url.searchParams.set('session', sessionCookie)
      return new Response(null, {
        status: 302,
        headers: {
          Location: url.toString()
        }
      })
    }
  }

  // --- Установка кук на mirsalnikov.ru и "очистка" query-параметров ---
  if (context.url.hostname === 'mirsalnikov.ru') {
    const url = new URL(context.url)
    const cartParam = url.searchParams.get('cart')
    const sessionParam = url.searchParams.get('session')

    // Обрабатываем миграцию только если есть параметры cart или session
    if ((cartParam || sessionParam) && !migratedCookie) {
      if (cartParam) {
        context.cookies.set('_cart', decodeURIComponent(cartParam), {
          path: '/',
          httpOnly: true,
          secure: true,
          sameSite: 'none',
          maxAge: 60 * 60 * 24 * 365
        })
      }
      if (sessionParam) {
        context.cookies.set('rti-session', decodeURIComponent(sessionParam), {
          path: '/',
          httpOnly: true,
          secure: true,
          sameSite: 'none',
          maxAge: 60 * 60 * 24 * 365
        })
      }

      // Устанавливаем куку миграции на mirsalnikov.ru
      context.cookies.set('2rti-cookies-migrated-success', 'true', {
        path: '/',
        httpOnly: true,
        secure: true,
        sameSite: 'none',
        maxAge: 60 * 60 * 24 * 365
      })

      // Редиректим обратно на rti-baltika.ru с параметром migrated=1
      url.searchParams.delete('cart')
      url.searchParams.delete('session')
      url.searchParams.set('migrated', '1')
      const backUrl = `https://rti-baltika.ru${url.pathname}${url.search}`

      return new Response(null, {
        status: 302,
        headers: {
          Location: backUrl
        }
      })
    }
  }

  isMobile.set(initialIsMobile)

  return next()
})
